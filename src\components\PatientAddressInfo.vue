<template>
  <div>
    <div class="row mt-2">
      <!-- Primeira linha: CEP, Estado, Cidade -->
      <div class="col-sm-4 col-lg-3 mb-2">
        <MaterialInput
          label="CEP"
          type="text"
          v-model="localData.endereco_cep"
          :input="handleFieldInput('endereco_cep', true)"
          mask="#####-###"
          :id="'paciente_enderecoCep' + (isMobile ? '_mobile' : '')"
          :readonly="!isEditing"
        />
      </div>
      <div class="col-sm-4 col-lg-4 mb-2">
        <MaterialInput
          label="Estado"
          type="text"
          v-model="localData.endereco_estado"
          :id="'paciente_enderecoEstado' + (isMobile ? '_mobile' : '')"
          :readonly="!isEditing"
          :input="handleFieldInput('endereco_estado')"
        />
      </div>
      <div class="col-sm-4 col-lg-5 mb-2">
        <MaterialInput
          label="Cidade"
          type="text"
          v-model="localData.endereco_cidade"
          :id="'paciente_enderecoCidade' + (isMobile ? '_mobile' : '')"
          :readonly="!isEditing"
          :input="handleFieldInput('endereco_cidade')"
        />
      </div>

      <!-- Segunda linha: Bairro, Logradouro, Número -->
      <div class="col-sm-4 col-lg-4 mb-2">
        <MaterialInput
          label="Bairro"
          type="text"
          v-model="localData.endereco_bairro"
          :id="'paciente_enderecoBairro' + (isMobile ? '_mobile' : '')"
          :readonly="!isEditing"
          :input="handleFieldInput('endereco_bairro')"
        />
      </div>
      <div class="col-sm-6 col-lg-6 mb-2">
        <MaterialInput
          label="Logradouro"
          type="text"
          v-model="localData.endereco_logradouro"
          :id="'paciente_enderecoLogradouro' + (isMobile ? '_mobile' : '')"
          :readonly="!isEditing"
          :input="handleFieldInput('endereco_logradouro')"
        />
      </div>
      <div class="col-sm-2 col-lg-2 mb-2">
        <MaterialInput
          label="Nº"
          type="text"
          v-model="localData.endereco_numero"
          :id="'paciente_enderecoNumero' + (isMobile ? '_mobile' : '')"
          :ref="isMobile ? null : 'endereco_numero'"
          :readonly="!isEditing"
          :input="handleFieldInput('endereco_numero')"
        />
      </div>

      <!-- Terceira linha: Complemento (ocupando toda a largura disponível) -->
      <div class="col-12">
        <MaterialInput
          label="Complemento"
          type="text"
          v-model="localData.endereco_complemento"
          :id="'paciente_enderecoComplemento' + (isMobile ? '_mobile' : '')"
          :readonly="!isEditing"
          :input="handleFieldInput('endereco_complemento')"
        />
      </div>
    </div>
  </div>
</template>

<script>
import MaterialInput from "@/components/MaterialInput.vue";

export default {
  name: "PatientAddressInfo",
  components: {
    MaterialInput
  },
  props: {
    paciente: {
      type: Object,
      required: true
    },
    isEditing: {
      type: Boolean,
      default: false
    },
    isMobile: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      localData: {
        endereco_cep: this.paciente.endereco_cep,
        endereco_estado: this.paciente.endereco_estado,
        endereco_cidade: this.paciente.endereco_cidade,
        endereco_bairro: this.paciente.endereco_bairro,
        endereco_logradouro: this.paciente.endereco_logradouro,
        endereco_numero: this.paciente.endereco_numero,
        endereco_complemento: this.paciente.endereco_complemento
      }
    };
  },
  watch: {
    paciente: {
      handler(newVal) {
        this.localData = {
          endereco_cep: newVal.endereco_cep,
          endereco_estado: newVal.endereco_estado,
          endereco_cidade: newVal.endereco_cidade,
          endereco_bairro: newVal.endereco_bairro,
          endereco_logradouro: newVal.endereco_logradouro,
          endereco_numero: newVal.endereco_numero,
          endereco_complemento: newVal.endereco_complemento
        };
      },
      deep: true
    }
  },
  methods: {
    updateField(field, value) {
      this.$emit('update:field', { field, value });
    },
    handleFieldInput(fieldName, isCep = false) {
      return (event) => {
        if (event && event.target) {
          this.localData[fieldName] = event.target.value;
          this.updateField(fieldName, this.localData[fieldName]);

          // Se for o campo CEP, emitir o evento get-endereco
          if (isCep) {
            this.$emit('get-endereco', event);
          }
        }
      };
    }
  }
};
</script>
